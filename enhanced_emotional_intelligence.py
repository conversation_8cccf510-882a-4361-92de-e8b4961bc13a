"""
Enhanced Emotional Intelligence System for AI Companion.
Advanced psychological profiling, trauma-aware responses, and therapeutic techniques.
"""

import asyncio
import logging
import json
import re
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum

from models import EmotionType, InteractionType
from gemini_service import OptimizedGeminiService
from advanced_emotional_intelligence import (
    AdvancedEmotionalIntelligence, MentalHealthRisk, TherapeuticTechnique,
    PsychologicalModel, EmotionalInsight, PsychologicalProfile
)

logger = logging.getLogger(__name__)

class AttachmentStyle(Enum):
    """Attachment styles based on attachment theory."""
    SECURE = "secure"
    ANXIOUS_PREOCCUPIED = "anxious_preoccupied"
    DISMISSIVE_AVOIDANT = "dismissive_avoidant"
    FEARFUL_AVOIDANT = "fearful_avoidant"
    UNKNOWN = "unknown"

class CopingStrategy(Enum):
    """Coping strategies for stress and emotional regulation."""
    PROBLEM_FOCUSED = "problem_focused"
    EMOTION_FOCUSED = "emotion_focused"
    AVOIDANCE = "avoidance"
    SOCIAL_SUPPORT = "social_support"
    MINDFULNESS = "mindfulness"
    COGNITIVE_REFRAMING = "cognitive_reframing"
    BEHAVIORAL_ACTIVATION = "behavioral_activation"

class PersonalityTrait(Enum):
    """Big Five personality traits."""
    OPENNESS = "openness"
    CONSCIENTIOUSNESS = "conscientiousness"
    EXTRAVERSION = "extraversion"
    AGREEABLENESS = "agreeableness"
    NEUROTICISM = "neuroticism"

@dataclass
class EmotionalPattern:
    """Represents an emotional pattern over time."""
    emotion: EmotionType
    frequency: float  # How often this emotion appears
    intensity_avg: float  # Average intensity
    triggers: List[str]  # Common triggers
    duration_avg: float  # Average duration in hours
    time_patterns: Dict[str, float]  # Time-based patterns (morning, evening, etc.)
    seasonal_patterns: Dict[str, float]  # Seasonal patterns
    
@dataclass
class TherapeuticRelationship:
    """Models the therapeutic relationship with the user."""
    user_id: str
    alliance_strength: float = 0.5  # Therapeutic alliance strength (0-1)
    trust_level: float = 0.5  # User's trust in the AI (0-1)
    engagement_level: float = 0.5  # User's engagement (0-1)
    resistance_level: float = 0.0  # Resistance to therapeutic interventions (0-1)
    preferred_techniques: List[TherapeuticTechnique] = field(default_factory=list)
    avoided_topics: List[str] = field(default_factory=list)
    breakthrough_moments: List[Dict[str, Any]] = field(default_factory=list)
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

@dataclass
class CrisisProtocol:
    """Crisis intervention protocol."""
    risk_level: MentalHealthRisk
    immediate_actions: List[str]
    safety_plan: Dict[str, Any]
    emergency_contacts: List[str]
    follow_up_schedule: List[str]
    professional_referral: bool = False

class EnhancedEmotionalIntelligence(AdvancedEmotionalIntelligence):
    """
    Enhanced emotional intelligence with advanced psychological profiling,
    trauma-aware responses, and sophisticated therapeutic techniques.
    """
    
    def __init__(self, gemini_service: OptimizedGeminiService):
        super().__init__(gemini_service)
        
        # Enhanced psychological modeling
        self.attachment_styles: Dict[str, AttachmentStyle] = {}
        self.personality_profiles: Dict[str, Dict[PersonalityTrait, float]] = {}
        self.coping_strategies: Dict[str, List[CopingStrategy]] = defaultdict(list)
        self.emotional_patterns: Dict[str, List[EmotionalPattern]] = defaultdict(list)
        
        # Therapeutic relationship management
        self.therapeutic_relationships: Dict[str, TherapeuticRelationship] = {}
        self.crisis_protocols: Dict[str, CrisisProtocol] = {}
        
        # Advanced pattern recognition
        self.emotional_triggers: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        self.resilience_factors: Dict[str, List[str]] = defaultdict(list)
        self.growth_indicators: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # Therapeutic technique effectiveness tracking
        self.technique_effectiveness: Dict[str, Dict[TherapeuticTechnique, float]] = defaultdict(lambda: defaultdict(float))
        
        # Initialize advanced prompts
        self._initialize_advanced_prompts()
    
    def _initialize_advanced_prompts(self):
        """Initialize advanced therapeutic prompts."""
        self.attachment_analysis_prompt = """
        Analyze the following conversation for attachment style indicators:
        
        Conversation: {conversation_text}
        
        Look for patterns indicating:
        - Secure: Comfortable with intimacy and autonomy
        - Anxious-Preoccupied: Seeks approval, fears abandonment
        - Dismissive-Avoidant: Values independence, uncomfortable with closeness
        - Fearful-Avoidant: Wants close relationships but fears getting hurt
        
        Provide analysis in JSON format:
        {{
            "attachment_style": "secure|anxious_preoccupied|dismissive_avoidant|fearful_avoidant",
            "confidence": 0.0-1.0,
            "indicators": ["list", "of", "specific", "indicators"],
            "relationship_patterns": ["patterns", "observed"]
        }}
        """
        
        self.personality_analysis_prompt = """
        Analyze personality traits from this conversation:
        
        Conversation: {conversation_text}
        
        Rate each Big Five trait (0.0-1.0):
        - Openness: Creativity, curiosity, openness to experience
        - Conscientiousness: Organization, discipline, goal-directed behavior
        - Extraversion: Sociability, assertiveness, positive emotions
        - Agreeableness: Cooperation, trust, empathy
        - Neuroticism: Emotional instability, anxiety, negative emotions
        
        JSON format:
        {{
            "openness": 0.0-1.0,
            "conscientiousness": 0.0-1.0,
            "extraversion": 0.0-1.0,
            "agreeableness": 0.0-1.0,
            "neuroticism": 0.0-1.0,
            "confidence": 0.0-1.0
        }}
        """
        
        self.coping_strategy_prompt = """
        Identify coping strategies from this conversation:
        
        User message: {message}
        Context: {context}
        
        Identify which coping strategies the user is employing or might benefit from:
        - Problem-focused: Direct action to solve the problem
        - Emotion-focused: Managing emotional response
        - Avoidance: Avoiding the stressor
        - Social support: Seeking help from others
        - Mindfulness: Present-moment awareness
        - Cognitive reframing: Changing thought patterns
        - Behavioral activation: Engaging in positive activities
        
        JSON format:
        {{
            "current_strategies": ["list", "of", "strategies", "being", "used"],
            "recommended_strategies": ["list", "of", "recommended", "strategies"],
            "effectiveness_assessment": {{"strategy": "effectiveness_score"}},
            "specific_techniques": ["specific", "techniques", "to", "suggest"]
        }}
        """
    
    async def analyze_attachment_style(self, user_id: str, conversation_history: List[Dict[str, Any]]) -> AttachmentStyle:
        """Analyze user's attachment style from conversation patterns."""
        try:
            # Prepare conversation text
            conversation_text = "\n".join([
                f"{msg.get('role', 'user')}: {msg.get('content', '')}"
                for msg in conversation_history[-10:]  # Last 10 messages
            ])
            
            if not conversation_text.strip():
                return AttachmentStyle.UNKNOWN
            
            # Generate analysis
            prompt = self.attachment_analysis_prompt.format(conversation_text=conversation_text)
            response = await self.gemini_service.generate_response_async(prompt)
            
            # Parse response
            analysis = self._parse_json_response(response)
            if analysis and 'attachment_style' in analysis:
                style_str = analysis['attachment_style'].upper()
                try:
                    attachment_style = AttachmentStyle(style_str.lower())
                    self.attachment_styles[user_id] = attachment_style
                    
                    # Update psychological profile
                    if user_id in self.psychological_profiles:
                        profile = self.psychological_profiles[user_id]
                        profile.attachment_style = attachment_style.value
                        profile.last_updated = datetime.now(timezone.utc)
                    
                    return attachment_style
                except ValueError:
                    return AttachmentStyle.UNKNOWN
            
            return AttachmentStyle.UNKNOWN
            
        except Exception as e:
            logger.error(f"Error analyzing attachment style for {user_id}: {e}")
            return AttachmentStyle.UNKNOWN
    
    async def analyze_personality_traits(self, user_id: str, conversation_history: List[Dict[str, Any]]) -> Dict[PersonalityTrait, float]:
        """Analyze Big Five personality traits."""
        try:
            conversation_text = "\n".join([
                f"{msg.get('role', 'user')}: {msg.get('content', '')}"
                for msg in conversation_history[-15:]  # More context for personality
            ])
            
            if not conversation_text.strip():
                return {}
            
            prompt = self.personality_analysis_prompt.format(conversation_text=conversation_text)
            response = await self.gemini_service.generate_response_async(prompt)
            
            analysis = self._parse_json_response(response)
            if analysis:
                personality_scores = {}
                for trait in PersonalityTrait:
                    score = analysis.get(trait.value, 0.5)
                    personality_scores[trait] = max(0.0, min(1.0, float(score)))
                
                self.personality_profiles[user_id] = personality_scores
                return personality_scores
            
            return {}
            
        except Exception as e:
            logger.error(f"Error analyzing personality traits for {user_id}: {e}")
            return {}
    
    async def identify_coping_strategies(self, user_id: str, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Identify and recommend coping strategies."""
        try:
            prompt = self.coping_strategy_prompt.format(
                message=message,
                context=json.dumps(context, default=str)
            )
            
            response = await self.gemini_service.generate_response_async(prompt)
            analysis = self._parse_json_response(response)
            
            if analysis:
                # Update user's coping strategy profile
                current_strategies = analysis.get('current_strategies', [])
                for strategy_name in current_strategies:
                    try:
                        strategy = CopingStrategy(strategy_name.lower().replace(' ', '_'))
                        if strategy not in self.coping_strategies[user_id]:
                            self.coping_strategies[user_id].append(strategy)
                    except ValueError:
                        continue
                
                return analysis
            
            return {}
            
        except Exception as e:
            logger.error(f"Error identifying coping strategies for {user_id}: {e}")
            return {}
    
    async def build_therapeutic_relationship(self, user_id: str, interaction_quality: float, user_feedback: Optional[str] = None):
        """Build and maintain therapeutic relationship."""
        if user_id not in self.therapeutic_relationships:
            self.therapeutic_relationships[user_id] = TherapeuticRelationship(user_id=user_id)
        
        relationship = self.therapeutic_relationships[user_id]
        
        # Update alliance strength based on interaction quality
        relationship.alliance_strength = (
            relationship.alliance_strength * 0.9 + interaction_quality * 0.1
        )
        
        # Analyze user feedback if provided
        if user_feedback:
            feedback_sentiment = await self._analyze_feedback_sentiment(user_feedback)
            
            if feedback_sentiment > 0.6:  # Positive feedback
                relationship.trust_level = min(1.0, relationship.trust_level + 0.05)
                relationship.engagement_level = min(1.0, relationship.engagement_level + 0.03)
            elif feedback_sentiment < 0.4:  # Negative feedback
                relationship.resistance_level = min(1.0, relationship.resistance_level + 0.02)
        
        # Update engagement based on conversation frequency
        relationship.engagement_level = max(0.0, relationship.engagement_level - 0.01)  # Natural decay
        
        relationship.last_updated = datetime.now(timezone.utc)
    
    async def generate_trauma_informed_response(
        self, 
        user_id: str, 
        message: str, 
        emotional_insight: EmotionalInsight,
        context: Dict[str, Any]
    ) -> str:
        """Generate trauma-informed therapeutic response."""
        try:
            # Get therapeutic relationship
            relationship = self.therapeutic_relationships.get(user_id)
            
            # Assess trauma indicators
            trauma_indicators = self._assess_trauma_indicators(message, emotional_insight)
            
            # Select appropriate therapeutic approach
            therapeutic_approach = self._select_therapeutic_approach(
                emotional_insight, relationship, trauma_indicators
            )
            
            # Generate response based on approach
            if trauma_indicators['high_risk']:
                return await self._generate_crisis_response(user_id, message, emotional_insight)
            elif trauma_indicators['trauma_present']:
                return await self._generate_trauma_aware_response(user_id, message, emotional_insight, relationship)
            else:
                return await self._generate_supportive_response(user_id, message, emotional_insight, relationship)
                
        except Exception as e:
            logger.error(f"Error generating trauma-informed response: {e}")
            return "I'm here to support you. Would you like to share more about how you're feeling?"
    
    def _assess_trauma_indicators(self, message: str, emotional_insight: EmotionalInsight) -> Dict[str, bool]:
        """Assess trauma indicators in the message."""
        message_lower = message.lower()
        
        # High-risk indicators
        high_risk_patterns = [
            r'\b(kill|die|suicide|end it all|can\'t go on)\b',
            r'\b(hurt myself|self harm|cutting)\b',
            r'\b(no point|hopeless|worthless)\b'
        ]
        
        # Trauma indicators
        trauma_patterns = [
            r'\b(flashback|nightmare|triggered|ptsd)\b',
            r'\b(abuse|assault|violence|trauma)\b',
            r'\b(panic|dissociat|numb|detach)\b'
        ]
        
        high_risk = any(re.search(pattern, message_lower) for pattern in high_risk_patterns)
        trauma_present = any(re.search(pattern, message_lower) for pattern in trauma_patterns)
        
        # Also check emotional insight
        if emotional_insight.risk_assessment.value >= MentalHealthRisk.HIGH.value:
            high_risk = True
        
        return {
            'high_risk': high_risk,
            'trauma_present': trauma_present,
            'emotional_intensity': emotional_insight.emotional_intensity > 0.8
        }
    
    def _select_therapeutic_approach(
        self, 
        emotional_insight: EmotionalInsight,
        relationship: Optional[TherapeuticRelationship],
        trauma_indicators: Dict[str, bool]
    ) -> PsychologicalModel:
        """Select the most appropriate therapeutic approach."""
        if trauma_indicators['high_risk']:
            return PsychologicalModel.TRAUMA_INFORMED
        elif trauma_indicators['trauma_present']:
            return PsychologicalModel.TRAUMA_INFORMED
        elif emotional_insight.emotional_intensity > 0.8:
            return PsychologicalModel.DIALECTICAL_BEHAVIORAL
        elif relationship and relationship.alliance_strength > 0.7:
            return PsychologicalModel.COGNITIVE_BEHAVIORAL
        else:
            return PsychologicalModel.POSITIVE_PSYCHOLOGY
    
    async def _generate_crisis_response(self, user_id: str, message: str, emotional_insight: EmotionalInsight) -> str:
        """Generate crisis intervention response."""
        crisis_responses = [
            "I'm very concerned about what you're going through right now. Your safety is the most important thing. If you're in immediate danger, please contact emergency services (911) or a crisis helpline.",
            "I can hear how much pain you're in, and I want you to know that you're not alone. There are people who want to help. Please reach out to a crisis counselor or emergency services if you're having thoughts of hurting yourself.",
            "What you're feeling right now is temporary, even though it might not feel that way. Please stay safe and reach out for immediate help if you need it. You matter, and there are people who care about you."
        ]
        
        # Create crisis protocol if needed
        if user_id not in self.crisis_protocols:
            self.crisis_protocols[user_id] = CrisisProtocol(
                risk_level=emotional_insight.risk_assessment,
                immediate_actions=["Contact emergency services", "Reach out to trusted person", "Use safety plan"],
                safety_plan={"emergency_contacts": ["911", "Crisis Text Line: Text HOME to 741741"]},
                emergency_contacts=["911", "988 Suicide & Crisis Lifeline"],
                follow_up_schedule=["Immediate", "24 hours", "1 week"],
                professional_referral=True
            )
        
        return crisis_responses[0]  # Use the first, most direct response
    
    async def _analyze_feedback_sentiment(self, feedback: str) -> float:
        """Analyze sentiment of user feedback (0.0 = very negative, 1.0 = very positive)."""
        try:
            prompt = f"""
            Analyze the sentiment of this feedback on a scale of 0.0 to 1.0:
            
            Feedback: "{feedback}"
            
            Return only a number between 0.0 and 1.0 where:
            0.0 = Very negative
            0.5 = Neutral
            1.0 = Very positive
            """
            
            response = await self.gemini_service.generate_response_async(prompt)
            try:
                sentiment = float(response.strip())
                return max(0.0, min(1.0, sentiment))
            except ValueError:
                return 0.5  # Default to neutral
                
        except Exception as e:
            logger.error(f"Error analyzing feedback sentiment: {e}")
            return 0.5
    
    def get_therapeutic_relationship_status(self, user_id: str) -> Dict[str, Any]:
        """Get therapeutic relationship status for a user."""
        if user_id not in self.therapeutic_relationships:
            return {"status": "not_established"}
        
        relationship = self.therapeutic_relationships[user_id]
        
        return {
            "alliance_strength": relationship.alliance_strength,
            "trust_level": relationship.trust_level,
            "engagement_level": relationship.engagement_level,
            "resistance_level": relationship.resistance_level,
            "preferred_techniques": [t.value for t in relationship.preferred_techniques],
            "breakthrough_moments": len(relationship.breakthrough_moments),
            "last_updated": relationship.last_updated.isoformat()
        }
    
    def get_user_emotional_patterns(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive emotional patterns for a user."""
        patterns = self.emotional_patterns.get(user_id, [])
        
        return {
            "dominant_emotions": [p.emotion.value for p in patterns[:3]],
            "emotional_triggers": dict(self.emotional_triggers.get(user_id, {})),
            "coping_strategies": [s.value for s in self.coping_strategies.get(user_id, [])],
            "attachment_style": self.attachment_styles.get(user_id, AttachmentStyle.UNKNOWN).value,
            "personality_traits": {
                trait.value: score for trait, score in self.personality_profiles.get(user_id, {}).items()
            },
            "resilience_factors": self.resilience_factors.get(user_id, [])
        }
