"""
WhatsApp Bot Integration for AI Companion System.
Production-ready deployment for emotional friendship and mental health support.
"""

import asyncio
import logging
import json
import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import aiohttp
from flask import Flask, request, jsonify
import threading
from queue import Queue

from ultra_fast_conversation_service import UltraFastConversationService
from enhanced_emotional_intelligence import EnhancedEmotionalIntelligence
from mental_health_data_platform import MentalHealthDataPlatform, DataPrivacyLevel
from models import EmotionType

logger = logging.getLogger(__name__)

@dataclass
class WhatsAppMessage:
    """WhatsApp message structure."""
    from_number: str
    to_number: str
    message_id: str
    message_body: str
    timestamp: datetime
    message_type: str = "text"
    media_url: Optional[str] = None

@dataclass
class UserSession:
    """User session management."""
    user_id: str
    phone_number: str
    conversation_state: str = "active"
    last_interaction: datetime = None
    message_count: int = 0
    consent_given: bool = False
    crisis_mode: bool = False

class WhatsAppBotService:
    """
    Production-ready WhatsApp bot for AI companion system.
    Handles emotional support, crisis intervention, and mental health conversations.
    """
    
    def __init__(self, webhook_verify_token: str, access_token: str, phone_number_id: str):
        # WhatsApp API configuration
        self.webhook_verify_token = webhook_verify_token
        self.access_token = access_token
        self.phone_number_id = phone_number_id
        self.api_base_url = "https://graph.facebook.com/v18.0"
        
        # Core AI services
        self.conversation_service = UltraFastConversationService()
        self.mental_health_platform = MentalHealthDataPlatform()
        
        # User session management
        self.user_sessions: Dict[str, UserSession] = {}
        self.message_queue = Queue()
        
        # Flask app for webhook
        self.app = Flask(__name__)
        self._setup_routes()
        
        # Background processing
        self.processing_thread = None
        self.running = False
        
        # Crisis intervention
        self.crisis_keywords = [
            'suicide', 'kill myself', 'end it all', 'can\'t go on', 'no point living',
            'hurt myself', 'self harm', 'cutting', 'overdose', 'jump off'
        ]
        
        # Welcome and onboarding messages
        self.welcome_message = """
🤗 Welcome to your AI Companion!

I'm here to provide emotional support and be a friend when you need one. Our conversations are private and designed to help you feel heard and understood.

*Important:*
• I'm an AI assistant, not a replacement for professional mental health care
• If you're in crisis, please contact emergency services (911) or a crisis helpline
• Your privacy is protected - conversations are anonymized for research to help others

Type 'help' for more information or just start chatting! 💙
        """.strip()
        
        self.help_message = """
🆘 *How I can help:*

💬 *Chat freely* - Share your thoughts, feelings, and experiences
🧠 *Emotional support* - Get empathetic responses and coping strategies  
📊 *Track patterns* - I'll remember our conversations to provide better support
🔒 *Privacy first* - Your data is anonymized and secure

*Crisis Resources:*
• 🚨 Emergency: 911
• 💙 Crisis Text Line: Text HOME to 741741
• 📞 988 Suicide & Crisis Lifeline: 988

*Commands:*
• 'help' - Show this message
• 'privacy' - Privacy information
• 'crisis' - Crisis resources
• 'feedback' - Share feedback

Just talk to me naturally - I'm here to listen! 🤗
        """.strip()
        
        self.privacy_message = """
🔒 *Your Privacy Matters*

*What we collect:*
• Your messages (anonymized)
• Emotional patterns (for better support)
• General conversation topics

*What we DON'T collect:*
• Your real name or personal details
• Location data
• Contact information

*How we use data:*
• Improve AI responses
• Research mental health patterns (anonymized)
• Help mental health professionals understand trends

*Your rights:*
• You can stop anytime
• Data is automatically deleted after 1 year
• No data is shared with identifiable information

Type 'consent' to give permission for anonymized research use.
        """.strip()
    
    def _setup_routes(self):
        """Setup Flask routes for WhatsApp webhook."""
        
        @self.app.route('/webhook', methods=['GET'])
        def verify_webhook():
            """Verify webhook with WhatsApp."""
            mode = request.args.get('hub.mode')
            token = request.args.get('hub.verify_token')
            challenge = request.args.get('hub.challenge')
            
            if mode == 'subscribe' and token == self.webhook_verify_token:
                logger.info("Webhook verified successfully")
                return challenge
            else:
                logger.warning("Webhook verification failed")
                return 'Verification failed', 403
        
        @self.app.route('/webhook', methods=['POST'])
        def handle_webhook():
            """Handle incoming WhatsApp messages."""
            try:
                data = request.get_json()
                logger.info(f"Received webhook data: {json.dumps(data, indent=2)}")
                
                # Process webhook data
                if self._is_valid_webhook_data(data):
                    self.message_queue.put(data)
                    return jsonify({'status': 'success'})
                else:
                    logger.warning("Invalid webhook data received")
                    return jsonify({'status': 'invalid_data'}), 400
                    
            except Exception as e:
                logger.error(f"Error handling webhook: {e}")
                return jsonify({'status': 'error', 'message': str(e)}), 500
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """Health check endpoint."""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'active_sessions': len(self.user_sessions),
                'queue_size': self.message_queue.qsize()
            })
    
    def _is_valid_webhook_data(self, data: Dict[str, Any]) -> bool:
        """Validate webhook data structure."""
        try:
            entry = data.get('entry', [])
            if not entry:
                return False
            
            changes = entry[0].get('changes', [])
            if not changes:
                return False
            
            value = changes[0].get('value', {})
            messages = value.get('messages', [])
            
            return len(messages) > 0
        except Exception:
            return False
    
    def _extract_message_from_webhook(self, data: Dict[str, Any]) -> Optional[WhatsAppMessage]:
        """Extract WhatsApp message from webhook data."""
        try:
            entry = data['entry'][0]
            changes = entry['changes'][0]
            value = changes['value']
            
            if 'messages' not in value:
                return None
            
            message_data = value['messages'][0]
            
            return WhatsAppMessage(
                from_number=message_data['from'],
                to_number=value['metadata']['phone_number_id'],
                message_id=message_data['id'],
                message_body=message_data.get('text', {}).get('body', ''),
                timestamp=datetime.fromtimestamp(int(message_data['timestamp']), tz=timezone.utc),
                message_type=message_data['type']
            )
            
        except Exception as e:
            logger.error(f"Error extracting message from webhook: {e}")
            return None
    
    async def send_whatsapp_message(self, to_number: str, message: str) -> bool:
        """Send message via WhatsApp API."""
        try:
            url = f"{self.api_base_url}/{self.phone_number_id}/messages"
            
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'messaging_product': 'whatsapp',
                'to': to_number,
                'type': 'text',
                'text': {'body': message}
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Message sent successfully to {to_number}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to send message: {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error sending WhatsApp message: {e}")
            return False
    
    def _get_or_create_user_session(self, phone_number: str) -> UserSession:
        """Get or create user session."""
        if phone_number not in self.user_sessions:
            self.user_sessions[phone_number] = UserSession(
                user_id=f"whatsapp_{phone_number}",
                phone_number=phone_number,
                last_interaction=datetime.now(timezone.utc)
            )
        
        session = self.user_sessions[phone_number]
        session.last_interaction = datetime.now(timezone.utc)
        session.message_count += 1
        
        return session
    
    def _is_crisis_message(self, message: str) -> bool:
        """Detect crisis indicators in message."""
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in self.crisis_keywords)
    
    async def _handle_crisis_message(self, session: UserSession, message: str) -> str:
        """Handle crisis intervention."""
        session.crisis_mode = True
        
        crisis_response = """
🚨 *I'm very concerned about you right now.*

Your safety is the most important thing. Please reach out for immediate help:

*Immediate Support:*
• 🚨 Emergency Services: 911
• 💙 Crisis Text Line: Text HOME to 741741  
• 📞 988 Suicide & Crisis Lifeline: 988
• 🌐 Chat online: suicidepreventionlifeline.org

*You are not alone.* There are people who want to help you through this difficult time.

Would you like me to help you find local mental health resources, or would you prefer to talk about what's making you feel this way?

*Remember: I'm an AI and can't replace professional help, but I'm here to listen and support you.*
        """.strip()
        
        # Record crisis event (anonymized)
        await self.mental_health_platform.record_emotional_interaction(
            user_id=session.user_id,
            emotion=EmotionType.FEAR,  # Crisis often involves fear/desperation
            intensity=1.0,  # Maximum intensity for crisis
            message=message,
            context={'crisis': True, 'intervention_needed': True},
            intervention_type='crisis_intervention'
        )
        
        return crisis_response
    
    async def _handle_command(self, session: UserSession, command: str) -> str:
        """Handle special commands."""
        command_lower = command.lower().strip()
        
        if command_lower == 'help':
            return self.help_message
        elif command_lower == 'privacy':
            return self.privacy_message
        elif command_lower == 'crisis':
            return """
🆘 *Crisis Resources*

*Immediate Help:*
• 🚨 Emergency: 911
• 💙 Crisis Text Line: Text HOME to 741741
• 📞 988 Suicide & Crisis Lifeline: 988

*Online Support:*
• suicidepreventionlifeline.org
• crisistextline.org
• nami.org (National Alliance on Mental Illness)

*International:*
• befrienders.org (worldwide)

You matter. Help is available. 💙
            """.strip()
        elif command_lower == 'consent':
            session.consent_given = True
            return """
✅ *Thank you for your consent!*

Your anonymized conversation data will help improve mental health support for others. Your privacy remains fully protected.

You can withdraw consent anytime by typing 'withdraw consent'.
            """.strip()
        elif command_lower == 'withdraw consent':
            session.consent_given = False
            return """
✅ *Consent withdrawn.*

Your data will no longer be used for research. Our conversation continues with full privacy protection.
            """.strip()
        elif command_lower == 'feedback':
            return """
💭 *We'd love your feedback!*

How has your experience been? What's working well? What could be better?

Your feedback helps us improve support for everyone. Just type your thoughts naturally!
            """.strip()
        
        return None
    
    async def _process_regular_message(self, session: UserSession, message: str) -> str:
        """Process regular conversation message."""
        try:
            # Use ultra-fast conversation service
            response, processing_time, metrics = await self.conversation_service.process_message_ultra_fast(
                user_id=session.user_id,
                message=message,
                context={
                    'platform': 'whatsapp',
                    'phone_number': session.phone_number,
                    'message_count': session.message_count,
                    'consent_given': session.consent_given
                }
            )
            
            # Record interaction for research (if consent given)
            if session.consent_given:
                # Extract emotion from metrics if available
                emotion_str = metrics.get('emotional_insight', 'neutral')
                try:
                    emotion = EmotionType(emotion_str.lower())
                except ValueError:
                    emotion = EmotionType.NEUTRAL
                
                await self.mental_health_platform.record_emotional_interaction(
                    user_id=session.user_id,
                    emotion=emotion,
                    intensity=0.5,  # Default intensity
                    message=message,
                    context={
                        'platform': 'whatsapp',
                        'processing_time': processing_time,
                        'cache_hit': metrics.get('cache_hit', False)
                    },
                    intervention_type='conversational_support'
                )
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing regular message: {e}")
            return "I'm having some technical difficulties right now, but I'm still here for you. How are you feeling?"
    
    async def process_message(self, whatsapp_message: WhatsAppMessage):
        """Process incoming WhatsApp message."""
        try:
            session = self._get_or_create_user_session(whatsapp_message.from_number)
            message = whatsapp_message.message_body.strip()
            
            # Handle first-time users
            if session.message_count == 1:
                await self.send_whatsapp_message(whatsapp_message.from_number, self.welcome_message)
                return
            
            # Check for crisis indicators
            if self._is_crisis_message(message):
                response = await self._handle_crisis_message(session, message)
                await self.send_whatsapp_message(whatsapp_message.from_number, response)
                return
            
            # Handle commands
            command_response = await self._handle_command(session, message)
            if command_response:
                await self.send_whatsapp_message(whatsapp_message.from_number, command_response)
                return
            
            # Process regular conversation
            response = await self._process_regular_message(session, message)
            await self.send_whatsapp_message(whatsapp_message.from_number, response)
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            # Send fallback response
            fallback_response = "I'm experiencing some technical issues, but I'm still here to listen. How are you feeling right now?"
            await self.send_whatsapp_message(whatsapp_message.from_number, fallback_response)
    
    def start_background_processing(self):
        """Start background message processing."""
        self.running = True
        
        def process_messages():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def message_processor():
                while self.running:
                    try:
                        if not self.message_queue.empty():
                            webhook_data = self.message_queue.get()
                            whatsapp_message = self._extract_message_from_webhook(webhook_data)
                            
                            if whatsapp_message:
                                await self.process_message(whatsapp_message)
                        
                        await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
                        
                    except Exception as e:
                        logger.error(f"Error in message processor: {e}")
                        await asyncio.sleep(1)
            
            loop.run_until_complete(message_processor())
        
        self.processing_thread = threading.Thread(target=process_messages)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        logger.info("Background message processing started")
    
    def stop_background_processing(self):
        """Stop background message processing."""
        self.running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
        logger.info("Background message processing stopped")
    
    def run_webhook_server(self, host: str = '0.0.0.0', port: int = 5000, debug: bool = False):
        """Run the Flask webhook server."""
        logger.info(f"Starting WhatsApp bot webhook server on {host}:{port}")
        self.start_background_processing()
        
        try:
            self.app.run(host=host, port=port, debug=debug, threaded=True)
        finally:
            self.stop_background_processing()
    
    def get_bot_statistics(self) -> Dict[str, Any]:
        """Get bot usage statistics."""
        total_users = len(self.user_sessions)
        active_users = len([s for s in self.user_sessions.values() 
                           if s.last_interaction > datetime.now(timezone.utc) - timedelta(hours=24)])
        crisis_users = len([s for s in self.user_sessions.values() if s.crisis_mode])
        consented_users = len([s for s in self.user_sessions.values() if s.consent_given])
        
        return {
            'total_users': total_users,
            'active_users_24h': active_users,
            'crisis_interventions': crisis_users,
            'research_consent_rate': consented_users / total_users if total_users > 0 else 0,
            'queue_size': self.message_queue.qsize(),
            'platform_stats': self.mental_health_platform.get_anonymized_statistics()
        }

# Environment configuration
def create_whatsapp_bot() -> WhatsAppBotService:
    """Create WhatsApp bot with environment configuration."""
    webhook_verify_token = os.getenv('WHATSAPP_WEBHOOK_VERIFY_TOKEN', 'your_verify_token_here')
    access_token = os.getenv('WHATSAPP_ACCESS_TOKEN', 'your_access_token_here')
    phone_number_id = os.getenv('WHATSAPP_PHONE_NUMBER_ID', 'your_phone_number_id_here')
    
    return WhatsAppBotService(
        webhook_verify_token=webhook_verify_token,
        access_token=access_token,
        phone_number_id=phone_number_id
    )

if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and run bot
    bot = create_whatsapp_bot()
    bot.run_webhook_server(debug=False)
